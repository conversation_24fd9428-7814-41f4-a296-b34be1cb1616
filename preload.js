/**
 * ملف preload.js المبسط
 * يقوم بتعريف واجهة برمجة التطبيق (API) للتواصل بين العمليات
 */

const { contextBridge, ipcRenderer } = require('electron');
const path = require('path');

// تسجيل رسالة للتأكد من تحميل preload.js
console.log('=== بدء تحميل preload.js ===');
console.log('مسار preload.js:', __filename);
console.log('مجلد preload.js:', path.dirname(__filename));

// تعريف دالة لتسجيل الأخطاء
const logError = (error, context) => {
  console.error(`خطأ في ${context || 'preload.js'}:`, error);

  // محاولة تسجيل الخطأ في ملف السجل إذا كان ذلك ممكنًا
  try {
    ipcRenderer.invoke('log-error', {
      message: error.message,
      stack: error.stack,
      context: context || 'preload.js'
    });
  } catch (logError) {
    console.error('فشل في تسجيل الخطأ عبر IPC:', logError);
  }
};

// تعريف API
const api = {
  // وظيفة اختبار بسيطة
  test: () => 'API is working!',

  // وظيفة invoke الأساسية
  invoke: (channel, data) => {
    console.log(`Invoking channel: ${channel} with data:`, data);
    return ipcRenderer.invoke(channel, data);
  },

  // إضافة مستمعي الأحداث
  on: (channel, callback) => {
    console.log(`Adding listener for channel: ${channel}`);
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },

  // إزالة مستمعي الأحداث
  removeListener: (channel, callback) => {
    console.log(`Removing listener for channel: ${channel}`);
    ipcRenderer.removeListener(channel, callback);
  },

  // إرسال إشعار بالحاجة للتحديث
  sendRefreshEvent: (target) => {
    console.log(`إرسال إشعار بالحاجة للتحديث للهدف: ${target}`);
    return ipcRenderer.invoke('send-refresh-event', {
      target,
      timestamp: new Date().toISOString()
    });
  },

  // وظيفة إعادة تحميل الصفحة
  reloadPage: () => {
    console.log('إعادة تحميل الصفحة من خلال api.reloadPage');
    if (window.location && typeof window.location.reload === 'function') {
      window.location.reload();
      return true;
    }
    return ipcRenderer.invoke('reload-page');
  },

  // واجهة الأصناف
  items: {
    getAll: (forceRefresh = false) => ipcRenderer.invoke('get-all-items', forceRefresh),
    getById: (id) => ipcRenderer.invoke('get-item-by-id', id),
    search: (searchTerm) => ipcRenderer.invoke('search-items', searchTerm),
    add: (item) => ipcRenderer.invoke('add-item', item),
    clearCache: (operation = '', itemId = null) => ipcRenderer.invoke('clear-items-cache', operation, itemId),
    update: (id, updates) => {
      const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
      return ipcRenderer.invoke('update-item', id, updates, currentUserRole);
    },
    delete: (id) => {
      const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
      return ipcRenderer.invoke('delete-item', id, currentUserRole);
    },
    refreshAllWindows: () => {
      ipcRenderer.invoke('clear-items-cache', 'refresh-all-windows', null);
      ipcRenderer.send('refresh-items-all-windows');
      return { success: true };
    }
  },

  // واجهة المخزون
  inventory: {
    getAll: () => ipcRenderer.invoke('get-all-inventory'),
    getForItem: (itemId) => ipcRenderer.invoke('get-inventory-for-item', itemId),
    sync: () => ipcRenderer.invoke('sync-inventory'),
    clearCache: () => ipcRenderer.invoke('clear-inventory-cache'),
    refresh: async () => {
      await ipcRenderer.invoke('clear-inventory-cache');
      return ipcRenderer.invoke('get-all-inventory', true);
    }
  },

  // واجهة الآلات
  machines: {
    getAll: () => ipcRenderer.invoke('get-machines'),
    getPaginated: (page, pageSize, filters) => ipcRenderer.invoke('get-machines-paginated', { page, pageSize, filters }),
    add: (machine) => ipcRenderer.invoke('add-machine', machine),
    update: (machine) => ipcRenderer.invoke('update-machine', machine),
    delete: (id) => ipcRenderer.invoke('delete-machine', id)
  },

  // واجهة المعاملات
  transactions: {
    add: (transaction) => ipcRenderer.invoke('add-transaction', transaction),
    getAll: (filters) => ipcRenderer.invoke('get-transactions-report', filters)
  },

  // واجهة الاسترجاع
  returns: {
    create: (returnData) => ipcRenderer.invoke('create-return-transaction', returnData),
    getAll: (filters) => ipcRenderer.invoke('get-all-return-transactions', filters),
    getById: (returnId) => ipcRenderer.invoke('get-return-transaction-by-id', returnId),
    getForCustomer: (customerId) => ipcRenderer.invoke('get-customer-return-transactions', customerId),
    getAvailableItems: (customerId) => ipcRenderer.invoke('get-customer-available-items-for-return', customerId),
    getAvailableQuantity: (itemId, customerId, originalTransactionId) =>
      ipcRenderer.invoke('get-available-quantity-for-return', { itemId, customerId, originalTransactionId }),
    clearCache: () => ipcRenderer.invoke('clear-return-transactions-cache'),
    checkStatus: () => ipcRenderer.invoke('check-return-system-status'),
    repair: () => ipcRenderer.invoke('repair-return-system'),
    checkAvailability: () => ipcRenderer.invoke('check-return-system-availability'),
    verifyInventory: (itemId) => ipcRenderer.invoke('verify-inventory-after-return', { itemId }),
    getReport: (filters) => ipcRenderer.invoke('get-return-transactions-report', filters)
  },

  // واجهة العملاء
  customers: {
    getAll: () => ipcRenderer.invoke('get-all-customers'),
    getById: (id) => ipcRenderer.invoke('get-customer-by-id', id),
    search: (searchTerm) => ipcRenderer.invoke('search-customers', searchTerm),
    getByType: (customerType) => ipcRenderer.invoke('get-customers-by-type', customerType),
    getSubCustomers: (parentId) => ipcRenderer.invoke('get-sub-customers', parentId),
    add: (customer) => ipcRenderer.invoke('add-customer', customer),
    update: (id, updates) => {
      const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
      return ipcRenderer.invoke('update-customer', id, updates, currentUserRole);
    },
    delete: (id) => {
      const currentUserRole = localStorage.getItem('currentUserRole') || 'employee';
      return ipcRenderer.invoke('delete-customer', id, currentUserRole);
    },
    getSalesHistory: (customerId) => ipcRenderer.invoke('get-customer-sales-history', customerId),
    getItemInfoForSale: (itemId) => ipcRenderer.invoke('get-item-info-for-sale', itemId),
    createSubInvoice: (parentInvoiceNumber, customerId, selectedItems) =>
      ipcRenderer.invoke('create-sub-invoice', parentInvoiceNumber, customerId, selectedItems),
    updateCustomersContext: (customers) => ipcRenderer.invoke('update-customers-context', customers),
    getInvoices: (filters) => ipcRenderer.invoke('get-customer-invoices', filters)
  },

  // واجهة الخزينة
  cashbox: {
    get: () => ipcRenderer.invoke('get-cashbox'),
    create: (initialBalance) => ipcRenderer.invoke('create-cashbox', { initial_balance: initialBalance }),
    updateInitialBalance: (initialBalance) => ipcRenderer.invoke('update-cashbox-initial-balance', { initial_balance: initialBalance }),
    addTransaction: (transaction) => ipcRenderer.invoke('add-cashbox-transaction', transaction),
    getTransactions: (filters) => ipcRenderer.invoke('get-cashbox-transactions', filters),
    fixProfitCalculation: () => ipcRenderer.invoke('fix-profit-calculation'),
    fixNegativeValues: () => ipcRenderer.invoke('fix-negative-cashbox-values')
  },

  // دالة إصلاح الأرباح (للوصول المباشر)
  updateProfitValues: () => ipcRenderer.invoke('update-profit-values'),

  // دوال إضافية للوصول المباشر
  getCashbox: () => ipcRenderer.invoke('get-cashbox'),
  getTransactionsWithFilters: (filters) => ipcRenderer.invoke('get-transactions-report', filters),

  // واجهة التقارير
  reports: {
    getInventoryReport: (options) => ipcRenderer.invoke('get-inventory-report', options),
    getTransactionsReport: (filters) => ipcRenderer.invoke('get-transactions-report', filters),
    getProfitsReport: (filters) => ipcRenderer.invoke('get-profits-report', filters),
    getSubCustomersSalesReport: (parentId, filters) => ipcRenderer.invoke('get-sub-customers-sales-report', parentId, filters),
    getCashboxReport: (filters) => ipcRenderer.invoke('get-cashbox-report', filters),
    getTopSellingItemsReport: (filters) => ipcRenderer.invoke('get-top-selling-items-report', filters),
    getTopCustomersReport: (filters) => ipcRenderer.invoke('get-top-customers-report', filters),
    getLowStockReport: (options) => ipcRenderer.invoke('get-low-stock-report', options)
  },

  // واجهة الأدوات المساعدة
  utils: {
    updateProfitValues: () => ipcRenderer.invoke('update-profit-values')
  },

  // واجهة المستخدمين
  users: {
    getAll: () => ipcRenderer.invoke('get-users'),
    add: (user) => ipcRenderer.invoke('add-user', user),
    update: (user) => ipcRenderer.invoke('update-user', user),
    delete: (id) => ipcRenderer.invoke('delete-user', id),
    login: (credentials) => ipcRenderer.invoke('login', credentials)
  },

  // واجهة قاعدة البيانات
  database: {
    backup: () => ipcRenderer.invoke('backup-database'),
    restore: (path) => ipcRenderer.invoke('restore-database', path),
    applyChanges: () => ipcRenderer.invoke('apply-database-changes')
  },

  // واجهة الإعدادات
  settings: {
    get: () => ipcRenderer.invoke('get-settings'),
    update: (settings) => ipcRenderer.invoke('update-settings', settings),
    getTheme: () => ipcRenderer.invoke('get-theme'),
    setTheme: (theme) => ipcRenderer.invoke('set-theme', theme),
    testIntegrationConnection: (url, apiKey, isInternet) =>
      ipcRenderer.invoke('test-integration-connection', { url, apiKey, isInternet }),
    syncWithSalesSystem: (options) => ipcRenderer.invoke('sync-with-sales-system', options)
  },

  // واجهة تحديثات التطبيق
  updates: {
    checkAppUpdates: (options) => ipcRenderer.invoke('check-app-updates', options),
    checkDatabaseUpdates: (options) => ipcRenderer.invoke('check-database-updates', options),
    selectAppUpdateFile: () => ipcRenderer.invoke('select-app-update-file'),
    selectDatabaseUpdateFile: () => ipcRenderer.invoke('select-database-update-file'),
    downloadAndInstallUpdate: () => ipcRenderer.invoke('download-and-install-update'),
    applyDatabaseUpdates: () => ipcRenderer.invoke('apply-database-updates'),
    getBackupsList: () => ipcRenderer.invoke('get-backups-list'),
    restoreDatabaseBackup: (backupPath) => ipcRenderer.invoke('restore-database-backup', backupPath),
    deleteBackup: (backupPath) => ipcRenderer.invoke('delete-backup', backupPath),
    getAutoUpdateSettings: () => ipcRenderer.invoke('get-auto-update-settings'),
    setAutoUpdateSettings: (settings) => ipcRenderer.invoke('set-auto-update-settings', settings)
  },

  // فتح URL في المتصفح الافتراضي
  openBrowser: (url) => ipcRenderer.invoke('open-browser', url),

  // نظام تسجيل الأخطاء
  logger: {
    debug: (message, ...args) => ipcRenderer.invoke('log-debug', { message, args }),
    info: (message, ...args) => ipcRenderer.invoke('log-info', { message, args }),
    warn: (message, ...args) => ipcRenderer.invoke('log-warn', { message, args }),
    error: (message, ...args) => ipcRenderer.invoke('log-error', { message, args }),
    exception: (error, context) => ipcRenderer.invoke('log-exception', {
      message: error.message,
      stack: error.stack,
      context
    })
  },

  // نظام الإشعارات - تم تعطيله
  notifications: {
    show: (message, type, timeout) => {
      console.log(`تم تجاهل الإشعار: ${message} (${type})`);
      return Promise.resolve({ success: true });
    }
  },

  // معلومات النظام
  system: {
    getInfo: () => ipcRenderer.invoke('get-system-info'),
    getMemoryUsage: () => ipcRenderer.invoke('get-memory-usage'),
    getCpuUsage: () => ipcRenderer.invoke('get-cpu-usage')
  }
};

// تصدير API إلى النافذة
try {
  console.log('[PRELOAD] تصدير API إلى النافذة...');
  contextBridge.exposeInMainWorld('api', api);
  console.log('[PRELOAD] تم تصدير API بنجاح');
} catch (error) {
  console.error('[PRELOAD] خطأ في تصدير API:', error);
  logError(error, 'API export');
}

// إضافة مستمع للإشعار المباشر لتحديث الخزينة
ipcRenderer.on('direct-cashbox-update', (event, data) => {
  console.log('[CASHBOX-FIX] تم استلام إشعار مباشر لتحديث الخزينة:', data);

  try {
    const customEvent = new CustomEvent('direct-cashbox-update', { detail: data });
    window.dispatchEvent(customEvent);
    console.log('[CASHBOX-FIX] تم إرسال حدث direct-cashbox-update إلى المتصفح');
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في إرسال حدث direct-cashbox-update:', error);
  }
});

// زيادة حد مستمعي الأحداث لتجنب تحذيرات تسريب الذاكرة
ipcRenderer.setMaxListeners(20);

// إضافة مستمع لإشعار تحديث الخزينة الفوري
ipcRenderer.on('cashbox-updated', (event, data) => {
  console.log('[INSTANT-CASHBOX] تم استلام إشعار تحديث الخزينة:', data);

  try {
    // إرسال حدث فوري لتحديث واجهة المستخدم
    const customEvent = new CustomEvent('cashbox-updated-ui', {
      detail: {
        ...data,
        instant: true,
        timestamp: new Date().toISOString(),
        _forceUpdate: Date.now()
      }
    });
    window.dispatchEvent(customEvent);
    console.log('[INSTANT-CASHBOX] تم إرسال حدث التحديث الفوري إلى المتصفح');

    // إرسال حدث إضافي للتأكد من التحديث
    setTimeout(() => {
      const additionalEvent = new CustomEvent('direct-cashbox-update', {
        detail: {
          ...data,
          instant: true,
          _forceUpdate: Date.now()
        }
      });
      window.dispatchEvent(additionalEvent);
      console.log('[INSTANT-CASHBOX] تم إرسال حدث التحديث الإضافي');
    }, 25);

  } catch (error) {
    console.error('[INSTANT-CASHBOX] خطأ في إرسال أحداث التحديث الفوري:', error);
  }
});

// إضافة مستمع لإشعار تحديث الأرباح
ipcRenderer.on('profits-updated', (event, data) => {
  console.log('[PROFITS-UPDATE] تم استلام إشعار تحديث الأرباح:', data);

  try {
    const customEvent = new CustomEvent('profits-updated', { detail: data });
    window.dispatchEvent(customEvent);
    console.log('[PROFITS-UPDATE] تم إرسال حدث profits-updated إلى المتصفح');
  } catch (error) {
    console.error('[PROFITS-UPDATE] خطأ في إرسال حدث profits-updated:', error);
  }
});

// إضافة مستمع لإشعار إضافة معاملة
ipcRenderer.on('transaction-added', (event, data) => {
  console.log('[TRANSACTION-ADDED] تم استلام إشعار إضافة معاملة:', data);

  try {
    const customEvent = new CustomEvent('transaction-added', { detail: data });
    window.dispatchEvent(customEvent);
    console.log('[TRANSACTION-ADDED] تم إرسال حدث transaction-added إلى المتصفح');
  } catch (error) {
    console.error('[TRANSACTION-ADDED] خطأ في إرسال حدث transaction-added:', error);
  }
});

// إضافة مستمع للتأكد من تحميل النافذة
window.addEventListener('DOMContentLoaded', () => {
  console.log('[PRELOAD] تم تحميل النافذة بنجاح');

  // التحقق من توفر window.api
  if (window.api) {
    console.log('[PRELOAD] window.api متوفر في النافذة');

    // اختبار بسيط للتأكد من عمل API
    try {
      if (typeof window.api.test === 'function') {
        const testResult = window.api.test();
        console.log('[PRELOAD] اختبار API نجح:', testResult);
      } else {
        console.log('[PRELOAD] API متوفر ولكن دالة test غير موجودة');
      }
    } catch (error) {
      console.error('[PRELOAD] فشل اختبار API:', error);
    }
  } else {
    console.warn('[PRELOAD] window.api غير متوفر في النافذة - قد يكون هذا طبيعي أثناء التحميل');
  }
});

// إضافة تحقق إضافي بعد تحميل الصفحة
window.addEventListener('load', () => {
  console.log('[PRELOAD] تم تحميل الصفحة بالكامل');

  // تحقق نهائي من window.api بعد تأخير قصير
  setTimeout(() => {
    if (window.api) {
      console.log('[PRELOAD] التحقق النهائي: window.api متوفر');

      // اختبار الوظائف الأساسية
      const basicFunctions = ['invoke', 'test'];
      basicFunctions.forEach(funcName => {
        if (typeof window.api[funcName] === 'function') {
          console.log(`[PRELOAD] ✅ ${funcName} متوفر`);
        } else {
          console.log(`[PRELOAD] ⚠️ ${funcName} غير متوفر`);
        }
      });
    } else {
      console.warn('[PRELOAD] التحقق النهائي: window.api غير متوفر - قد تحتاج لإعادة تحميل الصفحة');
    }
  }, 500);
});
