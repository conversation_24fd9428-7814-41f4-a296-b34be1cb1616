/**
 * وحدة التحقق من المعاملات المجمعة
 * تضمن هذه الوحدة التحقق من توفر جميع الأصناف في المعاملات المجمعة قبل تنفيذ أي منها
 */

import { validateInventoryAvailability, generateDetailedErrorMessage, generateShortErrorMessage } from './inventoryValidator';

/**
 * التحقق من صحة معاملات البيع المجمعة
 * @param {Array} transactions - قائمة المعاملات المطلوب التحقق منها
 * @param {Object} options - خيارات إضافية للتحقق
 * @returns {Object} نتيجة التحقق مع تفاصيل المعاملات غير الصالحة
 */
export const validateBatchSalesTransactions = async (transactions, options = {}) => {
  try {
    console.log('[BATCH-VALIDATOR] بدء التحقق من المعاملات المجمعة...');
    console.log('[BATCH-VALIDATOR] عدد المعاملات:', transactions.length);

    const validationResults = {
      isValid: true,
      validTransactions: [],
      invalidTransactions: [],
      inventoryValidation: null,
      errors: []
    };

    // التحقق من وجود معاملات للتحقق منها
    if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
      validationResults.isValid = false;
      validationResults.errors.push('لا توجد معاملات للتحقق منها');
      return validationResults;
    }

    // استخراج الأصناف من المعاملات للتحقق من المخزون
    const itemsToValidate = [];
    const transactionValidationErrors = [];

    for (let i = 0; i < transactions.length; i++) {
      const transaction = transactions[i];
      console.log(`[BATCH-VALIDATOR] التحقق من المعاملة ${i + 1}/${transactions.length}:`, transaction);

      try {
        // التحقق من صحة بيانات المعاملة الأساسية
        if (!transaction.item_id) {
          transactionValidationErrors.push(`المعاملة ${i + 1}: معرف الصنف مفقود`);
          continue;
        }

        if (!transaction.quantity || transaction.quantity <= 0) {
          transactionValidationErrors.push(`المعاملة ${i + 1}: الكمية غير صالحة (${transaction.quantity})`);
          continue;
        }

        if (transaction.transaction_type === 'sale' || transaction.transaction_type === 'withdrawal') {
          // إضافة الصنف إلى قائمة التحقق من المخزون
          const existingItem = itemsToValidate.find(item => item.item_id === transaction.item_id);
          
          if (existingItem) {
            // إذا كان الصنف موجود بالفعل، نجمع الكميات
            existingItem.quantity += transaction.quantity;
          } else {
            // إضافة صنف جديد
            itemsToValidate.push({
              item_id: transaction.item_id,
              item_name: transaction.item_name || `صنف ${transaction.item_id}`,
              quantity: transaction.quantity,
              transaction_index: i
            });
          }

          validationResults.validTransactions.push({
            ...transaction,
            index: i
          });
        } else {
          // معاملات أخرى (شراء، استلام، إلخ) لا تحتاج للتحقق من المخزون
          validationResults.validTransactions.push({
            ...transaction,
            index: i
          });
        }

      } catch (transactionError) {
        console.error(`[BATCH-VALIDATOR] خطأ في التحقق من المعاملة ${i + 1}:`, transactionError);
        transactionValidationErrors.push(`المعاملة ${i + 1}: ${transactionError.message}`);
      }
    }

    // إضافة أخطاء التحقق من المعاملات
    validationResults.errors.push(...transactionValidationErrors);

    // التحقق من المخزون للأصناف المطلوبة
    if (itemsToValidate.length > 0) {
      console.log(`[BATCH-VALIDATOR] التحقق من توفر ${itemsToValidate.length} صنف في المخزون...`);
      
      const inventoryValidation = await validateInventoryAvailability(itemsToValidate);
      validationResults.inventoryValidation = inventoryValidation;

      if (!inventoryValidation.isValid) {
        console.log('[BATCH-VALIDATOR] ❌ فشل التحقق من توفر بعض الأصناف في المخزون');
        validationResults.isValid = false;

        // إضافة المعاملات المتأثرة بنقص المخزون إلى قائمة المعاملات غير الصالحة
        const affectedTransactions = [];
        
        [...inventoryValidation.unavailableItems, ...inventoryValidation.insufficientItems].forEach(item => {
          const relatedTransactions = transactions.filter(t => t.item_id === item.item_id);
          affectedTransactions.push(...relatedTransactions.map((t, index) => ({
            ...t,
            index,
            inventoryError: item.error
          })));
        });

        validationResults.invalidTransactions.push(...affectedTransactions);
      } else {
        console.log('[BATCH-VALIDATOR] ✅ جميع الأصناف متوفرة في المخزون');
      }
    }

    // التحقق النهائي من صحة جميع المعاملات
    if (validationResults.errors.length > 0 || !validationResults.isValid) {
      validationResults.isValid = false;
    }

    const summary = {
      totalTransactions: transactions.length,
      validTransactions: validationResults.validTransactions.length,
      invalidTransactions: validationResults.invalidTransactions.length,
      itemsToCheck: itemsToValidate.length,
      errors: validationResults.errors.length
    };

    console.log('[BATCH-VALIDATOR] ملخص نتائج التحقق:', summary);

    if (validationResults.isValid) {
      console.log('[BATCH-VALIDATOR] ✅ جميع المعاملات صالحة للتنفيذ');
    } else {
      console.log('[BATCH-VALIDATOR] ❌ توجد مشاكل في بعض المعاملات');
    }

    return {
      ...validationResults,
      summary
    };

  } catch (error) {
    console.error('[BATCH-VALIDATOR] خطأ عام في التحقق من المعاملات المجمعة:', error);
    return {
      isValid: false,
      validTransactions: [],
      invalidTransactions: [],
      inventoryValidation: null,
      errors: [`خطأ عام في التحقق من المعاملات: ${error.message}`],
      summary: {
        totalTransactions: transactions?.length || 0,
        validTransactions: 0,
        invalidTransactions: 0,
        itemsToCheck: 0,
        errors: 1
      }
    };
  }
};

/**
 * إنشاء رسالة خطأ مفصلة للمعاملات المجمعة غير الصالحة
 * @param {Object} validationResult - نتيجة التحقق من المعاملات المجمعة
 * @returns {string} رسالة خطأ مفصلة
 */
export const generateBatchErrorMessage = (validationResult) => {
  if (validationResult.isValid) {
    return '';
  }

  let errorMessage = 'لا يمكن إتمام المعاملات المجمعة للأسباب التالية:\n\n';

  // أخطاء التحقق من المعاملات
  if (validationResult.errors.length > 0) {
    errorMessage += '❌ أخطاء في بيانات المعاملات:\n';
    validationResult.errors.forEach((error, index) => {
      errorMessage += `${index + 1}. ${error}\n`;
    });
    errorMessage += '\n';
  }

  // أخطاء المخزون
  if (validationResult.inventoryValidation && !validationResult.inventoryValidation.isValid) {
    errorMessage += '📦 مشاكل في المخزون:\n';
    errorMessage += generateDetailedErrorMessage(validationResult.inventoryValidation);
    errorMessage += '\n';
  }

  // المعاملات غير الصالحة
  if (validationResult.invalidTransactions.length > 0) {
    errorMessage += '🚫 معاملات غير صالحة:\n';
    validationResult.invalidTransactions.forEach((transaction, index) => {
      errorMessage += `${index + 1}. ${transaction.item_name || `صنف ${transaction.item_id}`}: ${transaction.inventoryError || 'خطأ غير محدد'}\n`;
    });
  }

  errorMessage += '\nيرجى تصحيح هذه المشاكل قبل المحاولة مرة أخرى.';

  return errorMessage;
};

/**
 * إنشاء رسالة خطأ مختصرة للمعاملات المجمعة
 * @param {Object} validationResult - نتيجة التحقق من المعاملات المجمعة
 * @returns {string} رسالة خطأ مختصرة
 */
export const generateBatchShortErrorMessage = (validationResult) => {
  if (validationResult.isValid) {
    return '';
  }

  const { invalidTransactions, errors, inventoryValidation } = validationResult;
  
  let errorParts = [];

  if (invalidTransactions.length > 0) {
    errorParts.push(`${invalidTransactions.length} معاملة غير صالحة`);
  }

  if (inventoryValidation && !inventoryValidation.isValid) {
    const inventoryShortError = generateShortErrorMessage(inventoryValidation);
    if (inventoryShortError) {
      errorParts.push(inventoryShortError);
    }
  }

  if (errors.length > 0) {
    errorParts.push(`${errors.length} خطأ في البيانات`);
  }

  if (errorParts.length === 0) {
    return 'لا يمكن إتمام المعاملات المجمعة';
  }

  return errorParts.join(' و ');
};

export default {
  validateBatchSalesTransactions,
  generateBatchErrorMessage,
  generateBatchShortErrorMessage
};
