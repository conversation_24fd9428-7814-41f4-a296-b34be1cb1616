/**
 * وحدة التحقق الشامل من توفر المخزون
 * تضمن هذه الوحدة التحقق من توفر جميع الأصناف والكميات المطلوبة قبل تنفيذ أي عملية بيع
 */

/**
 * التحقق الشامل من توفر جميع الأصناف والكميات المطلوبة
 * @param {Array} items - قائمة الأصناف المطلوب بيعها
 * @param {Object} options - خيارات إضافية للتحقق
 * @returns {Object} نتيجة التحقق مع تفاصيل الأصناف غير المتوفرة
 */
export const validateInventoryAvailability = async (items, options = {}) => {
  try {
    console.log('[INVENTORY-VALIDATOR] بدء التحقق الشامل من توفر المخزون...');
    console.log('[INVENTORY-VALIDATOR] عدد الأصناف المطلوب التحقق منها:', items.length);

    const validationResults = {
      isValid: true,
      unavailableItems: [],
      insufficientItems: [],
      validItems: [],
      totalRequestedItems: items.length,
      errors: []
    };

    // التحقق من وجود أصناف للتحقق منها
    if (!items || !Array.isArray(items) || items.length === 0) {
      validationResults.isValid = false;
      validationResults.errors.push('لا توجد أصناف للتحقق من توفرها');
      return validationResults;
    }

    // التحقق من كل صنف
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      console.log(`[INVENTORY-VALIDATOR] التحقق من الصنف ${i + 1}/${items.length}:`, item);

      try {
        // التحقق من صحة بيانات الصنف
        if (!item.item_id) {
          validationResults.errors.push(`الصنف رقم ${i + 1}: معرف الصنف مفقود`);
          validationResults.isValid = false;
          continue;
        }

        if (!item.quantity || item.quantity <= 0) {
          validationResults.errors.push(`الصنف رقم ${i + 1}: الكمية المطلوبة غير صالحة (${item.quantity})`);
          validationResults.isValid = false;
          continue;
        }

        // الحصول على معلومات الصنف من قاعدة البيانات
        let itemInfo = null;
        let itemName = item.item_name || `صنف ${item.item_id}`;

        // محاولة الحصول على معلومات الصنف من API
        if (window.api && window.api.customers && typeof window.api.customers.getItemInfoForSale === 'function') {
          try {
            itemInfo = await window.api.customers.getItemInfoForSale(item.item_id);
            if (itemInfo && itemInfo.name) {
              itemName = itemInfo.name;
            }
          } catch (apiError) {
            console.warn(`[INVENTORY-VALIDATOR] خطأ في الحصول على معلومات الصنف ${item.item_id} من API:`, apiError);
          }
        }

        // إذا لم نحصل على معلومات من API، نحاول الحصول عليها من المخزون المحلي
        if (!itemInfo && window.api && window.api.inventory && typeof window.api.inventory.getItem === 'function') {
          try {
            itemInfo = await window.api.inventory.getItem(item.item_id);
          } catch (inventoryError) {
            console.warn(`[INVENTORY-VALIDATOR] خطأ في الحصول على معلومات الصنف ${item.item_id} من المخزون:`, inventoryError);
          }
        }

        // التحقق من وجود الصنف
        if (!itemInfo) {
          validationResults.unavailableItems.push({
            item_id: item.item_id,
            item_name: itemName,
            requested_quantity: item.quantity,
            available_quantity: 0,
            error: 'الصنف غير موجود في المخزون'
          });
          validationResults.isValid = false;
          continue;
        }

        // التحقق من توفر الصنف للبيع
        if (itemInfo.available_for_sale === false) {
          validationResults.unavailableItems.push({
            item_id: item.item_id,
            item_name: itemName,
            requested_quantity: item.quantity,
            available_quantity: itemInfo.current_quantity || 0,
            error: itemInfo.message || 'الصنف غير متاح للبيع'
          });
          validationResults.isValid = false;
          continue;
        }

        // التحقق من توفر الكمية المطلوبة
        const availableQuantity = itemInfo.current_quantity || 0;
        const requestedQuantity = item.quantity;

        if (availableQuantity < requestedQuantity) {
          validationResults.insufficientItems.push({
            item_id: item.item_id,
            item_name: itemName,
            requested_quantity: requestedQuantity,
            available_quantity: availableQuantity,
            shortage: requestedQuantity - availableQuantity,
            error: `الكمية المتوفرة (${availableQuantity}) أقل من المطلوبة (${requestedQuantity})`
          });
          validationResults.isValid = false;
          continue;
        }

        // إذا وصلنا هنا، فالصنف متوفر بالكمية المطلوبة
        validationResults.validItems.push({
          item_id: item.item_id,
          item_name: itemName,
          requested_quantity: requestedQuantity,
          available_quantity: availableQuantity,
          item_info: itemInfo
        });

        console.log(`[INVENTORY-VALIDATOR] ✅ الصنف ${itemName} متوفر بالكمية المطلوبة`);

      } catch (itemError) {
        console.error(`[INVENTORY-VALIDATOR] خطأ في التحقق من الصنف ${item.item_id}:`, itemError);
        validationResults.errors.push(`خطأ في التحقق من الصنف ${itemName}: ${itemError.message}`);
        validationResults.isValid = false;
      }
    }

    // إنشاء ملخص النتائج
    const summary = {
      totalItems: items.length,
      validItems: validationResults.validItems.length,
      unavailableItems: validationResults.unavailableItems.length,
      insufficientItems: validationResults.insufficientItems.length,
      errors: validationResults.errors.length
    };

    console.log('[INVENTORY-VALIDATOR] ملخص نتائج التحقق:', summary);

    if (validationResults.isValid) {
      console.log('[INVENTORY-VALIDATOR] ✅ جميع الأصناف متوفرة بالكميات المطلوبة');
    } else {
      console.log('[INVENTORY-VALIDATOR] ❌ توجد مشاكل في توفر بعض الأصناف');
    }

    return {
      ...validationResults,
      summary
    };

  } catch (error) {
    console.error('[INVENTORY-VALIDATOR] خطأ عام في التحقق من المخزون:', error);
    return {
      isValid: false,
      unavailableItems: [],
      insufficientItems: [],
      validItems: [],
      totalRequestedItems: items?.length || 0,
      errors: [`خطأ عام في التحقق من المخزون: ${error.message}`],
      summary: {
        totalItems: items?.length || 0,
        validItems: 0,
        unavailableItems: 0,
        insufficientItems: 0,
        errors: 1
      }
    };
  }
};

/**
 * إنشاء رسالة خطأ مفصلة للأصناف غير المتوفرة
 * @param {Object} validationResult - نتيجة التحقق من المخزون
 * @returns {string} رسالة خطأ مفصلة
 */
export const generateDetailedErrorMessage = (validationResult) => {
  if (validationResult.isValid) {
    return '';
  }

  let errorMessage = 'لا يمكن إتمام عملية البيع للأسباب التالية:\n\n';

  // الأصناف غير الموجودة
  if (validationResult.unavailableItems.length > 0) {
    errorMessage += '🚫 أصناف غير متوفرة:\n';
    validationResult.unavailableItems.forEach((item, index) => {
      errorMessage += `${index + 1}. ${item.item_name}: ${item.error}\n`;
    });
    errorMessage += '\n';
  }

  // الأصناف ذات الكميات غير الكافية
  if (validationResult.insufficientItems.length > 0) {
    errorMessage += '⚠️ أصناف بكميات غير كافية:\n';
    validationResult.insufficientItems.forEach((item, index) => {
      errorMessage += `${index + 1}. ${item.item_name}:\n`;
      errorMessage += `   - المطلوب: ${item.requested_quantity}\n`;
      errorMessage += `   - المتوفر: ${item.available_quantity}\n`;
      errorMessage += `   - النقص: ${item.shortage}\n\n`;
    });
  }

  // أخطاء أخرى
  if (validationResult.errors.length > 0) {
    errorMessage += '❌ أخطاء أخرى:\n';
    validationResult.errors.forEach((error, index) => {
      errorMessage += `${index + 1}. ${error}\n`;
    });
  }

  errorMessage += '\nيرجى تصحيح هذه المشاكل قبل المحاولة مرة أخرى.';

  return errorMessage;
};

/**
 * إنشاء رسالة خطأ مختصرة للعرض في التنبيهات
 * @param {Object} validationResult - نتيجة التحقق من المخزون
 * @returns {string} رسالة خطأ مختصرة
 */
export const generateShortErrorMessage = (validationResult) => {
  if (validationResult.isValid) {
    return '';
  }

  const { unavailableItems, insufficientItems, errors } = validationResult;
  
  if (unavailableItems.length > 0 && insufficientItems.length > 0) {
    return `${unavailableItems.length} صنف غير متوفر و ${insufficientItems.length} صنف بكمية غير كافية`;
  } else if (unavailableItems.length > 0) {
    return `${unavailableItems.length} صنف غير متوفر في المخزون`;
  } else if (insufficientItems.length > 0) {
    return `${insufficientItems.length} صنف بكمية غير كافية`;
  } else if (errors.length > 0) {
    return `توجد ${errors.length} مشكلة في التحقق من المخزون`;
  }

  return 'لا يمكن إتمام عملية البيع';
};

export default {
  validateInventoryAvailability,
  generateDetailedErrorMessage,
  generateShortErrorMessage
};
