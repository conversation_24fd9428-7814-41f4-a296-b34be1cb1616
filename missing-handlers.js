/**
 * معالجات IPC المفقودة
 * يقوم بتسجيل معالجات IPC التي قد تكون مفقودة في الملفات الأخرى
 */

const { ipcMain } = require('electron');
const { logSystem, logError } = require('./error-handler');
const DatabaseManager = require('./database-singleton');
const { addPaymentStatusColumn } = require('./add-payment-status-column');

/**
 * تسجيل المعالجات المفقودة
 * @returns {boolean} - نجاح العملية
 */
function registerMissingHandlers() {
  try {
    logSystem('بدء تسجيل المعالجات المفقودة', 'info');

    // إضافة عمود payment_status إلى جدول transactions إذا لم يكن موجودًا
    const paymentStatusResult = addPaymentStatusColumn();
    logSystem(`نتيجة إضافة عمود payment_status: ${paymentStatusResult.message}`, 'info');

    // التحقق من وجود المعالج قبل تسجيله
    if (!ipcMain.eventNames().includes('test-channel')) {
      // معالج اختبار
      ipcMain.handle('test-channel', async (event, data) => {
        try {
          logSystem(`استدعاء test-channel مع البيانات: ${JSON.stringify(data)}`, 'info');
          return {
            success: true,
            message: 'تم استدعاء test-channel بنجاح',
            data
          };
        } catch (error) {
          logError(error, 'test-channel');
          return {
            success: false,
            error: error.message
          };
        }
      });
    } else {
      logSystem('معالج test-channel مسجل بالفعل، تم تجاوزه', 'info');
    }

    // معالج للحصول على معلومات النظام
    ipcMain.handle('get-system-info', async () => {
      try {
        const os = require('os');
        const systemInfo = {
          platform: os.platform(),
          arch: os.arch(),
          hostname: os.hostname(),
          uptime: os.uptime(),
          totalMemory: os.totalmem(),
          freeMemory: os.freemem(),
          cpus: os.cpus().length
        };

        logSystem(`تم الحصول على معلومات النظام: ${JSON.stringify(systemInfo)}`, 'info');
        return {
          success: true,
          systemInfo
        };
      } catch (error) {
        logError(error, 'get-system-info');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج للتحقق من صحة اتصال قاعدة البيانات
    ipcMain.handle('check-database-connection', async () => {
      try {
        const DatabaseManager = require('./database-singleton');
        const dbManager = DatabaseManager.getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          return {
            success: false,
            error: 'قاعدة البيانات غير متصلة'
          };
        }

        // محاولة تنفيذ استعلام بسيط للتحقق من صحة الاتصال
        const testQuery = db.prepare('SELECT 1');
        const testResult = testQuery.get();

        if (!testResult) {
          return {
            success: false,
            error: 'فشل في التحقق من صحة اتصال قاعدة البيانات'
          };
        }

        return {
          success: true,
          message: 'تم التحقق من صحة اتصال قاعدة البيانات بنجاح'
        };
      } catch (error) {
        logError(error, 'check-database-connection');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج لتصحيح قيم الربح السالبة
    ipcMain.handle('fix-negative-profits', async () => {
      try {
        logSystem('بدء تصحيح قيم الربح السالبة', 'info');

        const dbManager = DatabaseManager.getInstance();
        const db = dbManager.getConnection();

        if (!db) {
          return {
            success: false,
            error: 'قاعدة البيانات غير متصلة'
          };
        }

        // البحث عن المعاملات ذات قيم الربح السالبة
        const findNegativeProfitsQuery = `
          SELECT id, item_id, quantity, price, cost_price, profit
          FROM transactions
          WHERE profit < 0
        `;

        const negativeProfits = db.prepare(findNegativeProfitsQuery).all();
        logSystem(`تم العثور على ${negativeProfits.length} معاملة بقيم ربح سالبة`, 'info');

        if (negativeProfits.length === 0) {
          return {
            success: true,
            updatedCount: 0,
            message: 'لا توجد قيم ربح سالبة للتصحيح'
          };
        }

        // تحديث قيم الربح
        const updateProfitStmt = db.prepare(`
          UPDATE transactions
          SET profit = (price - cost_price) * quantity
          WHERE id = ?
        `);

        let updatedCount = 0;

        // بدء المعاملة
        db.exec('BEGIN TRANSACTION');

        try {
          for (const tx of negativeProfits) {
            const result = updateProfitStmt.run(tx.id);
            if (result.changes > 0) {
              updatedCount++;
            }
          }

          // تأكيد المعاملة
          db.exec('COMMIT');

          logSystem(`تم تصحيح ${updatedCount} قيمة ربح سالبة بنجاح`, 'info');

          return {
            success: true,
            updatedCount,
            message: `تم تصحيح ${updatedCount} قيمة ربح سالبة بنجاح`
          };
        } catch (updateError) {
          // التراجع عن المعاملة في حالة حدوث خطأ
          db.exec('ROLLBACK');
          throw updateError;
        }
      } catch (error) {
        logError(error, 'fix-negative-profits');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج للتحقق من تحديثات التطبيق
    ipcMain.handle('check-app-updates', async (event, options) => {
      try {
        logSystem(`التحقق من تحديثات التطبيق مع الخيارات: ${JSON.stringify(options)}`, 'info');

        // هنا يمكن إضافة منطق التحقق من التحديثات
        // هذا مجرد معالج وهمي للتوافق

        return {
          success: true,
          hasUpdates: false,
          currentVersion: '1.0.0',
          latestVersion: '1.0.0',
          message: 'لا توجد تحديثات متاحة'
        };
      } catch (error) {
        logError(error, 'check-app-updates');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج للتحقق من تحديثات قاعدة البيانات
    ipcMain.handle('check-database-updates', async (event, options) => {
      try {
        logSystem(`التحقق من تحديثات قاعدة البيانات مع الخيارات: ${JSON.stringify(options)}`, 'info');

        // هنا يمكن إضافة منطق التحقق من تحديثات قاعدة البيانات
        // هذا مجرد معالج وهمي للتوافق

        return {
          success: true,
          hasUpdates: false,
          currentVersion: '1.0.0',
          latestVersion: '1.0.0',
          message: 'لا توجد تحديثات متاحة لقاعدة البيانات'
        };
      } catch (error) {
        logError(error, 'check-database-updates');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج للحصول على قائمة النسخ الاحتياطية
    ipcMain.handle('get-backups-list', async () => {
      try {
        logSystem('الحصول على قائمة النسخ الاحتياطية', 'info');

        // هنا يمكن إضافة منطق الحصول على قائمة النسخ الاحتياطية
        // هذا مجرد معالج وهمي للتوافق

        return {
          success: true,
          backups: []
        };
      } catch (error) {
        logError(error, 'get-backups-list');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // معالج للحصول على إعدادات التحديث التلقائي
    ipcMain.handle('get-auto-update-settings', async () => {
      try {
        logSystem('الحصول على إعدادات التحديث التلقائي', 'info');

        // هنا يمكن إضافة منطق الحصول على إعدادات التحديث التلقائي
        // هذا مجرد معالج وهمي للتوافق

        return {
          success: true,
          settings: {
            enabled: false,
            checkFrequency: 'daily',
            lastCheck: null
          }
        };
      } catch (error) {
        logError(error, 'get-auto-update-settings');
        return {
          success: false,
          error: error.message
        };
      }
    });

    // تم إزالة معالجات Google Drive

    // معالج للحصول على عمليات الإرجاع للعميل
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-customer-return-transactions')) {
        ipcMain.handle('get-customer-return-transactions', async (event, customerId) => {
          try {
            logSystem(`الحصول على عمليات الإرجاع للعميل: ${customerId}`, 'info');

            // استيراد مدير عمليات الإرجاع
            const returnTransactionsManager = require('./return-transactions-manager');

            // التحقق من وجود الدالة
            if (typeof returnTransactionsManager.getCustomerReturnTransactions !== 'function') {
              throw new Error('دالة getCustomerReturnTransactions غير موجودة في مدير عمليات الإرجاع');
            }

            const returnTransactions = returnTransactionsManager.getCustomerReturnTransactions(customerId);
            return { success: true, returnTransactions };
          } catch (error) {
            logError(error, 'get-customer-return-transactions');
            return { success: false, returnTransactions: [], error: error.message };
          }
        });
        logSystem('تم تسجيل معالج get-customer-return-transactions بنجاح', 'info');
      } else {
        logSystem('معالج get-customer-return-transactions مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-customer-return-transactions');
    }

    // معالج للحصول على الأصناف المتاحة للإرجاع للعميل
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-customer-available-items-for-return')) {
        ipcMain.handle('get-customer-available-items-for-return', async (event, customerId) => {
          try {
            logSystem(`الحصول على الأصناف المتاحة للإرجاع للعميل: ${customerId}`, 'info');

            // الحصول على اتصال قاعدة البيانات
            const dbManager = DatabaseManager.getInstance();
            const db = dbManager.getConnection();

            if (!db) {
              throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
            }

            // استعلام للحصول على الأصناف المباعة للعميل
            const soldItemsQuery = `
              SELECT
                t.item_id,
                i.name as item_name,
                i.unit,
                SUM(t.quantity) as total_sold
              FROM transactions t
              JOIN items i ON t.item_id = i.id
              WHERE t.customer_id = ? AND t.transaction_type = 'sale'
              GROUP BY t.item_id
            `;

            // استعلام للحصول على الأصناف المسترجعة للعميل
            const returnedItemsQuery = `
              SELECT
                t.item_id,
                SUM(t.quantity) as total_returned
              FROM transactions t
              WHERE t.customer_id = ? AND t.transaction_type = 'return'
              GROUP BY t.item_id
            `;

            // تنفيذ الاستعلامات
            const soldItems = db.prepare(soldItemsQuery).all(customerId);
            const returnedItems = db.prepare(returnedItemsQuery).all(customerId);

            // تحويل الأصناف المسترجعة إلى كائن للوصول السريع
            const returnedItemsMap = {};
            returnedItems.forEach(item => {
              returnedItemsMap[item.item_id] = item.total_returned;
            });

            // حساب الكمية المتاحة للإرجاع لكل صنف
            const availableItems = soldItems
              .map(item => {
                const totalReturned = returnedItemsMap[item.item_id] || 0;
                const availableForReturn = item.total_sold - totalReturned;

                return {
                  item_id: item.item_id,
                  item_name: item.item_name,
                  unit: item.unit,
                  total_sold: item.total_sold,
                  total_returned: totalReturned,
                  available_for_return: availableForReturn
                };
              })
              .filter(item => item.available_for_return > 0); // فقط الأصناف التي لديها كمية متاحة للإرجاع

            return {
              success: true,
              items: availableItems,
              customer_id: customerId
            };
          } catch (error) {
            logError(error, 'get-customer-available-items-for-return');
            return {
              success: false,
              items: [],
              error: error.message,
              customer_id: customerId
            };
          }
        });
        logSystem('تم تسجيل معالج get-customer-available-items-for-return بنجاح', 'info');
      } else {
        logSystem('معالج get-customer-available-items-for-return مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-customer-available-items-for-return');
    }

    // معالج للحصول على فواتير العميل
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-customer-invoices')) {
        ipcMain.handle('get-customer-invoices', async (event, filters) => {
          try {
            // التحقق من وجود معرف العميل في الفلاتر
            if (!filters || !filters.customerId) {
              throw new Error('معرف العميل مطلوب للحصول على الفواتير');
            }

            const customerId = filters.customerId;
            logSystem(`الحصول على فواتير العميل: ${customerId} مع الفلاتر: ${JSON.stringify(filters)}`, 'info');

            // الحصول على اتصال قاعدة البيانات
            const dbManager = DatabaseManager.getInstance();
            const db = dbManager.getConnection();

            if (!db) {
              throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
            }

            // تحسين استعلام SQL للحصول على فواتير العميل مع تجميع المعاملات حسب رقم الفاتورة
            let query = `
              SELECT
                MIN(t.id) as id,
                t.invoice_number,
                MAX(t.transaction_date) as invoice_date,
                SUM(t.total_price) as total_amount,
                t.payment_status,
                MAX(t.notes) as notes,
                t.customer_id,
                c.name as customer_name,
                COUNT(DISTINCT t.id) as items_count
              FROM transactions t
              LEFT JOIN customers c ON t.customer_id = c.id
              WHERE t.transaction_type = 'sale' AND t.customer_id = ?
            `;

            const queryParams = [customerId];

            // إضافة فلتر التاريخ إذا كان موجودًا
            if (filters.startDate && filters.endDate) {
              // تحويل التواريخ إلى تنسيق ISO للمقارنة الصحيحة
              let startDateISO = filters.startDate;
              let endDateISO = filters.endDate;

              // إذا كان التاريخ بتنسيق YYYY/MM/DD، نحوله إلى ISO
              if (filters.startDate.includes('/')) {
                const startDate = new Date(filters.startDate.replace(/\//g, '-'));
                startDate.setHours(0, 0, 0, 0);
                startDateISO = startDate.toISOString();
              }

              if (filters.endDate.includes('/')) {
                const endDate = new Date(filters.endDate.replace(/\//g, '-'));
                endDate.setHours(23, 59, 59, 999);
                endDateISO = endDate.toISOString();
              }

              query += ' AND t.transaction_date BETWEEN ? AND ?';
              queryParams.push(startDateISO, endDateISO);

              logSystem(`تم تحويل فلاتر التاريخ: من ${filters.startDate} إلى ${startDateISO}, من ${filters.endDate} إلى ${endDateISO}`, 'info');
            }

            // تجميع النتائج حسب رقم الفاتورة
            query += ' GROUP BY t.invoice_number';

            // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
            query += ' ORDER BY invoice_date DESC';

            // تنفيذ الاستعلام
            const stmt = db.prepare(query);
            const invoices = stmt.all(...queryParams);

            logSystem(`تم العثور على ${invoices.length} فاتورة للعميل ${customerId}`, 'info');

            // الحصول على تفاصيل كل فاتورة (الأصناف)
            const invoicesWithItems = await Promise.all(invoices.map(async (invoice) => {
              // استعلام للحصول على أصناف الفاتورة
              const itemsQuery = `
                SELECT
                  t.id,
                  t.transaction_id,
                  t.item_id,
                  t.quantity,
                  t.price,
                  t.total_price,
                  t.profit,
                  i.name as item_name,
                  i.unit,
                  i.name as description
                FROM transactions t
                LEFT JOIN items i ON t.item_id = i.id
                WHERE t.invoice_number = ? AND t.transaction_type = 'sale'
              `;

              const itemsStmt = db.prepare(itemsQuery);
              const items = itemsStmt.all(invoice.invoice_number);

              // حساب إجمالي الربح للفاتورة
              const totalProfit = items.reduce((sum, item) => sum + (item.profit || 0), 0);

              // إضافة معلومات إضافية إلى الفاتورة
              return {
                ...invoice,
                items,
                total_profit: totalProfit,
                date: invoice.invoice_date, // إضافة حقل date للتوافق مع واجهة المستخدم
                profit_margin: invoice.total_amount > 0 ? (totalProfit / invoice.total_amount) * 100 : 0
              };
            }));

            // حساب إحصائيات الفواتير
            const totalAmount = invoicesWithItems.reduce((sum, invoice) => sum + (invoice.total_amount || 0), 0);
            const totalProfit = invoicesWithItems.reduce((sum, invoice) => sum + (invoice.total_profit || 0), 0);
            const paidInvoices = invoicesWithItems.filter(invoice => invoice.payment_status === 'paid').length;
            const unpaidInvoices = invoicesWithItems.length - paidInvoices;

            logSystem(`تم معالجة ${invoicesWithItems.length} فاتورة للعميل ${customerId} بنجاح`, 'info');

            return {
              success: true,
              invoices: invoicesWithItems,
              stats: {
                totalInvoices: invoicesWithItems.length,
                totalAmount,
                totalProfit,
                profitMargin: totalAmount > 0 ? (totalProfit / totalAmount) * 100 : 0,
                paidInvoices,
                unpaidInvoices
              }
            };
          } catch (error) {
            logError(error, 'get-customer-invoices');
            return { success: false, invoices: [], error: error.message };
          }
        });
        logSystem('تم تسجيل معالج get-customer-invoices بنجاح', 'info');
      } else {
        logSystem('معالج get-customer-invoices مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-customer-invoices');
    }

    // معالج لتنفيذ استعلام مباشر على قاعدة البيانات
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('execute-direct-query')) {
        ipcMain.handle('execute-direct-query', async (event, { query, params = [] }) => {
          try {
            logSystem(`تنفيذ استعلام مباشر: ${query}`, 'info');
            console.log(`[DIRECT-QUERY] تنفيذ استعلام مباشر: ${query}`);

            // الحصول على اتصال قاعدة البيانات
            const dbManager = DatabaseManager.getInstance();
            const db = dbManager.getConnection();

            if (!db) {
              throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
            }

            // تحديد نوع الاستعلام (SELECT أو غيره)
            const isSelectQuery = query.trim().toLowerCase().startsWith('select');

            try {
              let result;
              if (isSelectQuery) {
                // استعلام SELECT
                const stmt = db.prepare(query);
                if (Array.isArray(params) && params.length > 0) {
                  result = stmt.all(...params);
                } else {
                  result = stmt.all();
                }
                console.log(`[DIRECT-QUERY] تم تنفيذ استعلام SELECT بنجاح، تم استرجاع ${result.length} سجل`);
                return { success: true, data: result };
              } else {
                // استعلام غير SELECT (INSERT, UPDATE, DELETE)
                const stmt = db.prepare(query);
                if (Array.isArray(params) && params.length > 0) {
                  result = stmt.run(...params);
                } else {
                  result = stmt.run();
                }
                console.log(`[DIRECT-QUERY] تم تنفيذ استعلام غير SELECT بنجاح، تم تعديل ${result.changes} سجل`);
                return { success: true, changes: result.changes, lastInsertRowid: result.lastInsertRowid };
              }
            } catch (queryError) {
              console.error(`[DIRECT-QUERY] خطأ في تنفيذ الاستعلام: ${queryError.message}`);
              throw queryError;
            }
          } catch (error) {
            logError(error, 'execute-direct-query');
            console.error(`[DIRECT-QUERY] خطأ في تنفيذ الاستعلام المباشر: ${error.message}`);
            return { success: false, error: error.message };
          }
        });
        logSystem('تم تسجيل معالج execute-direct-query بنجاح', 'info');
        console.log('[DIRECT-QUERY] تم تسجيل معالج execute-direct-query بنجاح');
      } else {
        logSystem('معالج execute-direct-query مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج execute-direct-query');
      console.error(`[DIRECT-QUERY] خطأ في تسجيل معالج execute-direct-query: ${error.message}`);
    }

    // معالج للحصول على بيانات الرسوم البيانية للأرباح والمبيعات الشهرية
    try {
      // التحقق من وجود المعالج قبل تسجيله
      if (!ipcMain.eventNames().includes('get-monthly-charts-data')) {
        ipcMain.handle('get-monthly-charts-data', async (event, filters = {}) => {
          try {
            // تحسين رسالة السجل لتجنب عرض undefined
            const filtersSummary = {
              month: filters.month || 'غير محدد',
              startDate: filters.startDate || 'غير محدد',
              endDate: filters.endDate || 'غير محدد'
            };
            logSystem(`الحصول على بيانات الرسوم البيانية الشهرية مع الفلاتر: ${JSON.stringify(filtersSummary)}`, 'info');

            // الحصول على اتصال قاعدة البيانات
            const dbManager = DatabaseManager.getInstance();
            const db = dbManager.getConnection();

            if (!db) {
              throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
            }

            // تحديد نطاق التاريخ
            const now = new Date();
            let endDate = filters.endDate ? new Date(filters.endDate) : now;

            // تعيين تاريخ البداية ليكون يناير 2025 افتراضيًا
            let startDate = new Date(2025, 0, 1); // يناير 2025
            startDate.setHours(0, 0, 0, 0);

            // إذا كان هناك شهر محدد في الفلاتر، استخدمه
            if (filters.month) {
              const [year, month] = filters.month.split('-').map(Number);
              if (!isNaN(year) && !isNaN(month) && month >= 1 && month <= 12) {
                startDate = new Date(year, month - 1, 1);
                const endOfMonth = new Date(year, month, 0);
                endDate = new Date(year, month - 1, endOfMonth.getDate());
                endDate.setHours(23, 59, 59, 999);
                logSystem(`تصفية البيانات للشهر: ${year}-${month}`, 'info');
              }
            } else if (filters.startDate) {
              // إذا كان هناك تاريخ بداية محدد في الفلاتر، استخدمه
              const customStartDate = new Date(filters.startDate);
              if (!isNaN(customStartDate.getTime())) {
                startDate = customStartDate;
              }
            }

            // التأكد من صحة التواريخ قبل التسجيل
            const startDateStr = startDate.toISOString();
            const endDateStr = endDate.toISOString();
            logSystem(`نطاق التاريخ للرسوم البيانية: ${startDateStr} إلى ${endDateStr}`, 'info');

            // استعلام للحصول على بيانات المبيعات الشهرية
            const salesQuery = `
              SELECT
                strftime('%Y-%m', transaction_date) as month,
                SUM(CASE WHEN transaction_type = 'sale' THEN total_price ELSE 0 END) as sales_total,
                SUM(CASE WHEN transaction_type = 'purchase' THEN total_price ELSE 0 END) as purchases_total,
                SUM(CASE WHEN transaction_type = 'return' THEN total_price ELSE 0 END) as returns_total,
                SUM(CASE WHEN transaction_type = 'sale' THEN profit ELSE 0 END) as profit_total,
                COUNT(CASE WHEN transaction_type = 'sale' THEN 1 ELSE NULL END) as sales_count,
                COUNT(CASE WHEN transaction_type = 'purchase' THEN 1 ELSE NULL END) as purchases_count,
                COUNT(CASE WHEN transaction_type = 'return' THEN 1 ELSE NULL END) as returns_count
              FROM transactions
              WHERE transaction_date BETWEEN ? AND ?
              GROUP BY strftime('%Y-%m', transaction_date)
              ORDER BY month ASC
            `;

            const stmt = db.prepare(salesQuery);
            const monthlyData = stmt.all(startDateStr, endDateStr);

            logSystem(`تم الحصول على ${monthlyData.length} شهر من البيانات`, 'info');

            // تحويل البيانات إلى تنسيق مناسب للرسوم البيانية
            const months = [];
            const salesData = [];
            const profitData = [];

            // إنشاء مصفوفة بجميع الأشهر في النطاق
            // إذا كان هناك شهر محدد، نستخدم فقط هذا الشهر
            if (filters.month) {
              const [year, month] = filters.month.split('-').map(Number);
              if (!isNaN(year) && !isNaN(month) && month >= 1 && month <= 12) {
                const monthKey = `${year}-${String(month).padStart(2, '0')}`;
                months.push(monthKey);
              }
            } else {
              // وإلا نستخدم جميع الأشهر في النطاق
              let currentDate = new Date(startDate);
              while (currentDate <= endDate) {
                const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
                months.push(monthKey);
                currentDate.setMonth(currentDate.getMonth() + 1);
              }
            }

            // ملء البيانات لكل شهر
            months.forEach(monthKey => {
              const monthData = monthlyData.find(d => d.month === monthKey) || {
                month: monthKey,
                sales_total: 0,
                purchases_total: 0,
                returns_total: 0,
                profit_total: 0,
                sales_count: 0,
                purchases_count: 0,
                returns_count: 0
              };

              // تحويل الشهر إلى تنسيق مقروء
              const [year, month] = monthKey.split('-');
              const monthNames = ['يناير', 'فبراير', 'مارس', 'إبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
              const monthLabel = `${monthNames[parseInt(month) - 1]} ${year}`;

              // إضافة البيانات إلى المصفوفات
              salesData.push({
                month: monthKey,
                label: monthLabel,
                sales: monthData.sales_total || 0,
                purchases: monthData.purchases_total || 0,
                returns: monthData.returns_total || 0,
                salesCount: monthData.sales_count || 0,
                purchasesCount: monthData.purchases_count || 0,
                returnsCount: monthData.returns_count || 0
              });

              const profit = monthData.profit_total || 0;
              const sales = monthData.sales_total || 0;
              const profitMargin = sales > 0 ? (profit / sales) * 100 : 0;

              profitData.push({
                month: monthKey,
                label: monthLabel,
                profit: profit,
                sales: sales,
                profitMargin: profitMargin
              });
            });

            logSystem(`تم معالجة بيانات الرسوم البيانية الشهرية بنجاح`, 'info');

            return {
              success: true,
              salesData,
              profitData
            };
          } catch (error) {
            logError(error, 'get-monthly-charts-data');
            return {
              success: false,
              salesData: [],
              profitData: [],
              error: error.message
            };
          }
        });
        logSystem('تم تسجيل معالج get-monthly-charts-data بنجاح', 'info');
      } else {
        logSystem('معالج get-monthly-charts-data مسجل بالفعل، تم تجاوزه', 'info');
      }
    } catch (error) {
      logError(error, 'تسجيل معالج get-monthly-charts-data');
    }

    logSystem('تم تسجيل المعالجات المفقودة بنجاح', 'info');
    return true;
  } catch (error) {
    logError(error, 'registerMissingHandlers');
    return false;
  }
}

// تصدير الدوال
module.exports = {
  registerMissingHandlers
};
